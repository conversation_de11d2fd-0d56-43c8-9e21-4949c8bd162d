import torch
import json
import time
from pathlib import Path
from typing import List, Dict
import re
import chromadb
import streamlit as st
from llama_index.core import VectorStoreIndex, StorageContext, Settings, get_response_synthesizer
from llama_index.core.chat_engine import ContextChatEngine
from llama_index.core.memory import ChatMemoryBuffer
from llama_index.core.schema import TextNode, Document
from llama_index.embeddings.huggingface import HuggingFaceEmbedding
from llama_index.vector_stores.chroma import ChromaVectorStore
from llama_index.core import PromptTemplate
from llama_index.core.postprocessor import SentenceTransformerRerank
from llama_index.llms.openai_like import OpenAILike

from llama_index.retrievers.bm25 import BM25Retriever
from llama_index.core.retrievers import QueryFusionRetriever, BaseRetriever
from llama_index.core.schema import QueryBundle, NodeWithScore
from typing import List
import re
from datetime import datetime, timedelta
import base64
import nest_asyncio
import hashlib
import pickle

# 应用nest_asyncio补丁，以在Streamlit等现有事件循环中运行异步LlamaIndex代码
nest_asyncio.apply()

# ================== Streamlit页面配置 ==================
st.set_page_config(
    page_title="南水北调水利问答助手",
    page_icon="assets/Picture/lixiahe.png",
    layout="centered",
    initial_sidebar_state="auto"
)


# 新增: 加载自定义CSS文件的函数
def load_css(file_path):
    try:
        with open(file_path, encoding='utf-8') as f:
            st.markdown(f"<style>{f.read()}</style>", unsafe_allow_html=True)
    except FileNotFoundError:
        st.error(f"CSS文件未找到: {file_path}")


def disable_streamlit_watcher():
    """Patch Streamlit to disable file watcher"""

    def _on_script_changed(_):
        return

    from streamlit import runtime
    runtime.get_instance()._on_script_changed = _on_script_changed


# 新增：生成文本文件的函数
def generate_single_qa_text(question, answer):
    """生成单次问答的文本文件，完全支持中文"""
    content = "南水北调水利问答\n\n"
    content += f"问题:\n{question}\n\n"
    content += f"回答:\n{answer}\n\n"
    content += f"生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}"

    return content.encode('utf-8')


# ================================ 配置类 ================================
class Config:
    EMBED_MODEL_PATH = r"D:\pythonProject\embedding_model\BAAI\bge-large-zh-v1___5"
    RERANK_MODEL_PATH = r"D:\pythonProject\llms\BAAI\bge-reranker-base"  # 新增重排序模型路径

    DATA_DIR = "./data"
    VECTOR_DB_DIR = "./chroma_db"
    PERSIST_DIR = "./storage"

    COLLECTION_NAME = "chinese_labor_laws"
    TOP_K = 20  # 进一步扩大初筛范围，提高检索召回率
    RERANK_TOP_K = 3  # 严格重排序，只保留最相关的3个文档，提高信噪比
    
    # 相关度过滤配置
    RELEVANCE_THRESHOLD = 0.1  # 降低相关度阈值，提高检索召回率
    
    # 文本分块配置
    # 可选值: 'semantic_double_merge' (语义双重合并分块), 'semantic' (语义分块), 'sentence' (传统句子分块)
    CHUNKING_MODE = 'semantic'
    # 传统分块参数
    CHUNK_SIZE = 512
    CHUNK_OVERLAP = 300
    # 语义分块参数
    SEMANTIC_BREAKPOINT_THRESHOLD = 90  # 百分位数阈值，越低生成的节点越多
    # 语义双重合并分块参数
    SEMANTIC_DOUBLE_INITIAL_THRESHOLD = 0.4  # 初始分块阈值
    SEMANTIC_DOUBLE_APPENDING_THRESHOLD = 0.5  # 附加阈值
    SEMANTIC_DOUBLE_MERGING_THRESHOLD = 0.5  # 合并阈值
    SEMANTIC_DOUBLE_MAX_CHUNK_SIZE = 3000  # 最大块大小


# ================== 查询嵌入缓存类 ==================
class QueryEmbeddingCache:
    """
    查询嵌入缓存，避免重复计算相同查询的嵌入向量
    使用内存+磁盘双重缓存策略，大幅提升重复查询速度
    """
    def __init__(self, cache_dir="./cache/queries", max_memory_cache=100):
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        self.memory_cache = {}  # 内存缓存，访问更快
        self.max_memory_cache = max_memory_cache

    def _get_cache_key(self, query):
        """生成查询的缓存键"""
        return hashlib.md5(query.encode('utf-8')).hexdigest()

    def get_embedding(self, query, embed_model):
        """获取查询的嵌入向量，优先从缓存获取"""
        # 1. 先查内存缓存
        if query in self.memory_cache:
            print(f"🚀 从内存缓存获取查询嵌入: {query[:50]}...")
            return self.memory_cache[query]

        # 2. 再查磁盘缓存
        cache_key = self._get_cache_key(query)
        cache_file = self.cache_dir / f"{cache_key}.pkl"

        if cache_file.exists():
            try:
                with open(cache_file, 'rb') as f:
                    embedding = pickle.load(f)
                    print(f"💾 从磁盘缓存获取查询嵌入: {query[:50]}...")

                    # 加入内存缓存（LRU策略）
                    if len(self.memory_cache) >= self.max_memory_cache:
                        # 删除最旧的缓存项
                        oldest_key = next(iter(self.memory_cache))
                        del self.memory_cache[oldest_key]

                    self.memory_cache[query] = embedding
                    return embedding
            except Exception as e:
                print(f"读取缓存文件失败: {e}")

        # 3. 计算新嵌入并缓存
        print(f"🔄 计算新的查询嵌入: {query[:50]}...")
        embedding = embed_model.get_text_embedding(query)

        # 保存到磁盘缓存
        try:
            with open(cache_file, 'wb') as f:
                pickle.dump(embedding, f)
        except Exception as e:
            print(f"保存缓存文件失败: {e}")

        # 保存到内存缓存
        if len(self.memory_cache) >= self.max_memory_cache:
            oldest_key = next(iter(self.memory_cache))
            del self.memory_cache[oldest_key]
        self.memory_cache[query] = embedding

        return embedding

    def get_cached_response(self, cache_key):
        """获取缓存的完整响应"""
        # 1. 先查内存缓存
        if cache_key in self.memory_cache:
            print(f"🚀 从内存缓存获取完整响应: {cache_key[:30]}...")
            return self.memory_cache[cache_key]

        # 2. 再查磁盘缓存
        cache_file_key = self._get_cache_key(cache_key)
        cache_file = self.cache_dir / f"response_{cache_file_key}.pkl"

        if cache_file.exists():
            try:
                with open(cache_file, 'rb') as f:
                    response_data = pickle.load(f)
                    print(f"💾 从磁盘缓存获取完整响应: {cache_key[:30]}...")

                    # 加入内存缓存
                    if len(self.memory_cache) >= self.max_memory_cache:
                        oldest_key = next(iter(self.memory_cache))
                        del self.memory_cache[oldest_key]

                    self.memory_cache[cache_key] = response_data
                    return response_data
            except Exception as e:
                print(f"读取响应缓存文件失败: {e}")

        return None

    def cache_full_response(self, cache_key, response_data):
        """缓存完整响应"""
        # 保存到磁盘缓存
        try:
            cache_file_key = self._get_cache_key(cache_key)
            cache_file = self.cache_dir / f"response_{cache_file_key}.pkl"
            with open(cache_file, 'wb') as f:
                pickle.dump(response_data, f)
        except Exception as e:
            print(f"保存响应缓存文件失败: {e}")

        # 保存到内存缓存
        if len(self.memory_cache) >= self.max_memory_cache:
            oldest_key = next(iter(self.memory_cache))
            del self.memory_cache[oldest_key]
        self.memory_cache[cache_key] = response_data

    def clear_cache(self):
        """清除所有缓存"""
        self.memory_cache.clear()
        if self.cache_dir.exists():
            import shutil
            shutil.rmtree(self.cache_dir)
            self.cache_dir.mkdir(parents=True, exist_ok=True)
        print("🗑️ 查询缓存已清除")


# ================== 优化的模型管理器（单例模式 + 延迟加载）==================
class OptimizedModelManager:
    """
    优化的模型管理器，使用单例模式确保模型只加载一次，
    使用延迟加载在真正需要时才加载模型，大幅提升启动速度
    """
    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            # 延迟加载：初始化时不加载任何模型
            cls._instance._embed_model = None
            cls._instance._llm = None
            cls._instance._reranker = None
            cls._instance._loading_embed = False
            cls._instance._loading_llm = False
            cls._instance._loading_reranker = False
        return cls._instance

    def get_embed_model(self):
        """获取嵌入模型，首次调用时才加载"""
        if self._embed_model is None and not self._loading_embed:
            self._loading_embed = True
            print("🔄 首次加载嵌入模型，请稍候...")

            self._embed_model = HuggingFaceEmbedding(
                model_name=Config.EMBED_MODEL_PATH,
                device='cuda' if torch.cuda.is_available() else 'cpu'
            )

            # 设置全局Settings
            Settings.embed_model = self._embed_model
            print("✅ 嵌入模型加载完成")
            self._loading_embed = False

        return self._embed_model

    def get_llm(self):
        """获取LLM模型，首次调用时才加载"""
        if self._llm is None and not self._loading_llm:
            self._loading_llm = True
            print("🔄 首次加载LLM...")

            self._llm = OpenAILike(
                model="deepseek-chat",
                api_base="https://api.deepseek.com",
                api_key="***********************************",
                context_window=128000,
                is_chat_model=True,
                is_function_calling_model=False,
                max_tokens=4096,
                temperature=0.3,
                top_p=0.7
            )

            # 设置全局Settings
            Settings.llm = self._llm
            print("✅ LLM加载完成")
            self._loading_llm = False

        return self._llm

    def get_reranker(self):
        """获取重排序模型，首次调用时才加载"""
        if self._reranker is None and not self._loading_reranker:
            self._loading_reranker = True
            print("🔄 首次加载重排序模型...")

            self._reranker = SentenceTransformerRerank(
                model=Config.RERANK_MODEL_PATH,
                top_n=Config.RERANK_TOP_K
            )

            print("✅ 重排序模型加载完成")
            self._loading_reranker = False

        return self._reranker

    def get_all_models(self):
        """获取所有模型（兼容原有接口）"""
        return self.get_embed_model(), self.get_llm(), self.get_reranker()


# 保持向后兼容的初始化函数
@st.cache_resource(show_spinner="初始化优化模型管理器...")
def init_models():
    """
    优化后的模型初始化函数
    现在只创建模型管理器实例，不立即加载模型，启动速度大幅提升
    """
    manager = OptimizedModelManager()
    return manager.get_all_models()  # 为了兼容现有代码


# 新增：缓存NodeParser
@st.cache_resource(show_spinner="初始化文本分割器...")
def init_node_parser():
    """
    初始化语义感知文本分割器，根据内容语义自动调整块大小，避免语义割裂
    支持三种分块方式:
    1. 语义双重合并分块 (最先进，需要spaCy)
    2. 语义分块 (较先进)
    3. 传统句子分块 (回退方案)
    """
    # 从配置中获取分块模式
    chunking_mode = Config.CHUNKING_MODE
    
    try:
        if chunking_mode == 'semantic_double_merge':
            # 尝试导入语义双重合并分块所需的模块
            try:
                import spacy
                from llama_index.core.node_parser import (
                    SemanticDoubleMergingSplitterNodeParser,
                    LanguageConfig
                )
                
                # 检查是否已安装spaCy模型
                try:
                    nlp = spacy.load("zh_core_web_md")
                    print("成功加载中文spaCy模型")
                except OSError:
                    print("未找到中文spaCy模型，尝试使用英文模型")
                    try:
                        nlp = spacy.load("en_core_web_md")
                        print("成功加载英文spaCy模型")
                    except OSError:
                        raise ImportError("未找到所需的spaCy模型，请安装: python -m spacy download zh_core_web_md")
                
                # 配置语言设置
                language = "chinese" if "zh" in nlp.meta["lang"] else "english"
                config = LanguageConfig(
                    language=language,
                    spacy_model=nlp.meta["name"]
                )
                
                # 创建语义双重合并分块器，使用配置参数
                semantic_double_merger = SemanticDoubleMergingSplitterNodeParser(
                    language_config=config,
                    initial_threshold=Config.SEMANTIC_DOUBLE_INITIAL_THRESHOLD,
                    appending_threshold=Config.SEMANTIC_DOUBLE_APPENDING_THRESHOLD,
                    merging_threshold=Config.SEMANTIC_DOUBLE_MERGING_THRESHOLD,
                    max_chunk_size=Config.SEMANTIC_DOUBLE_MAX_CHUNK_SIZE
                )
                
                print(f"成功初始化语义双重合并分块器 (语言: {language})")
                return semantic_double_merger
                
            except (ImportError, Exception) as e:
                print(f"语义双重合并分块器初始化失败: {str(e)}，回退到语义分块器")
                chunking_mode = 'semantic'
        
        if chunking_mode == 'semantic':
            # 尝试导入语义分块所需的模块
            from llama_index.core.node_parser import SemanticSplitterNodeParser
            from llama_index.embeddings.huggingface import HuggingFaceEmbedding
            
            # 使用与系统相同的嵌入模型，保持一致性
            embed_model = HuggingFaceEmbedding(
                model_name=Config.EMBED_MODEL_PATH,
                device='cuda' if torch.cuda.is_available() else 'cpu'
            )
            
            # 创建语义分块器，使用配置参数
            semantic_splitter = SemanticSplitterNodeParser(
                buffer_size=1,  # 每次分析一个句子
                breakpoint_percentile_threshold=Config.SEMANTIC_BREAKPOINT_THRESHOLD,
                embed_model=embed_model
            )
            
            print("成功初始化语义分块器")
            return semantic_splitter
            
    except (ImportError, Exception) as e:
        print(f"语义分块器初始化失败: {str(e)}，回退到传统分块器")
    
    # 如果语义分块器初始化失败，回退到传统的SentenceSplitter
    from llama_index.core.node_parser import SentenceSplitter
    print("使用传统句子分块器")
    return SentenceSplitter(
        chunk_size=Config.CHUNK_SIZE,
        chunk_overlap=Config.CHUNK_OVERLAP
    )


@st.cache_resource(show_spinner="加载知识库中...")
def load_index():
    """
    根据官方文档推荐的最佳实践，从磁盘显式加载知识库的各个组件。
    修复：确保使用正确的嵌入模型
    """
    from llama_index.core.storage.docstore import SimpleDocumentStore
    persist_dir = Path(Config.PERSIST_DIR)
    db_dir = Path(Config.VECTOR_DB_DIR)
    docstore_path = persist_dir / "docstore.json"

    if not all([persist_dir.exists(), db_dir.exists(), docstore_path.exists()]):
        print("知识库目录或必要文件(docstore.json)不存在。")
        return None

    try:
        print("--- 正在加载知识库 ---")

        # 0. 确保使用正确的嵌入模型
        model_manager = OptimizedModelManager()
        embed_model = model_manager.get_embed_model()

        # 1. 显式加载向量数据库
        db = chromadb.PersistentClient(path=str(db_dir))
        chroma_collection = db.get_or_create_collection(Config.COLLECTION_NAME)
        vector_store = ChromaVectorStore(chroma_collection=chroma_collection)

        # 2. 显式加载文档库
        docstore = SimpleDocumentStore.from_persist_path(str(docstore_path))
        print(f"文档库加载成功，共 {len(docstore.docs)} 个文档。")

        # 3. 从加载的组件重建存储上下文和索引
        storage_context = StorageContext.from_defaults(
            docstore=docstore, vector_store=vector_store
        )
        # 修复：使用正确的嵌入模型创建索引
        index = VectorStoreIndex(
            nodes=[],
            storage_context=storage_context,
            embed_model=embed_model  # 明确指定嵌入模型
        )

        print("--- 知识库加载成功！---")
        return index
    except Exception as e:
        print(f"加载知识库失败: {e}")
        import traceback
        traceback.print_exc()
        return None


# ============================== 数据处理 ==============================
# ===========数据处理json格式数据 ==========
def load_and_validate_json_files(data_dir: str) -> List[Dict]:
    """加载并验证JSON法律文件"""
    json_files = list(Path(data_dir).glob("*.json"))
    assert json_files, f"未找到JSON文件于 {data_dir}"

    all_data = []
    for json_file in json_files:
        with open(json_file, 'r', encoding='utf-8') as f:
            try:
                data = json.load(f)
                # 验证数据结构
                if not isinstance(data, list):
                    raise ValueError(f"文件 {json_file.name} 根元素应为列表")
                for item in data:
                    if not isinstance(item, dict):
                        raise ValueError(f"文件 {json_file.name} 包含非字典元素")
                    for k, v in item.items():
                        if not isinstance(v, str):
                            raise ValueError(f"文件 {json_file.name} 中键 '{k}' 的值不是字符串")
                all_data.extend({
                                    "content": item,
                                    "metadata": {"source_file": json_file.name, "content_type": "json_item"}
                                } for item in data)
            except Exception as e:
                raise RuntimeError(f"加载文件 {json_file} 失败: {str(e)}")

    print(f"成功加载 {len(all_data)} 个法律文件条目")
    return all_data

# ===========数据处理PDF格式数据 ==========
from pdf2image import convert_from_path
import pytesseract
from pathlib import Path
from typing import List, Dict

# 设置 Tesseract 路径（Windows 专用）
pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'
def load_pdfs(data_dir: str) -> List[Dict]:
    pdf_files = list(Path(data_dir).rglob("*.pdf"))
    all_data = []
    skipped_files = 0

    for pdf_file in pdf_files:
        try:
            images = convert_from_path(pdf_file, dpi=300)  # 高分辨率提高识别率
            text = ""
            for img in images:
                text += pytesseract.image_to_string(img, lang='chi_sim')  # 简体中文

            if not text.strip() or len(text.strip()) < 20:
                print(f"警告：OCR后PDF文件 {pdf_file.name} 仍无内容，已跳过。")
                skipped_files += 1
                continue

            all_data.append({
                "content": text,
                "metadata": {
                    "source_file": str(pdf_file.relative_to(data_dir)),
                    "content_type": "pdf_document"
                }
            })
        except Exception as e:
            print(f"OCR处理PDF文件 {pdf_file.name} 时出错: {str(e)}")
            skipped_files += 1

    print(f"OCR后成功加载 {len(all_data)} 个 PDF 文件")
    if skipped_files > 0:
        print(f"有 {skipped_files} 个PDF文件被跳过")

    return all_data


# ===========数据处理excel格式数据 ==========
import pandas as pd
import numpy as np
from pathlib import Path
import concurrent.futures
from functools import lru_cache
import re
from datetime import datetime, timedelta

def extract_facility_name_smart(file_name, sheet_name, columns):
    """智能提取设施名称 - 通用方法，适用于任何Excel文件"""

    # 方法1: 从文件名提取（使用正则表达式模式）
    facility_patterns = [
        # 水利设施常见模式
        r'([^\\\/]+?(?:渠首|引水闸|节制闸|渡槽|泵站|水库|大坝|闸门|水闸))',
        r'([^\\\/]+?(?:河|江|湖|库|渠|沟|溪).*?(?:闸|坝|站|槽))',
        # 地名+设施类型模式
        r'([^\\\/]*?(?:陶岔|湍河|十二里河|刁河|严陵河|白河|丹江|汉江)[^\\\/]*?)',
        # 通用设施模式
        r'([^\\\/]+?(?:水利|水务|灌溉|排水|防洪)[^\\\/]*?)',
    ]

    for pattern in facility_patterns:
        match = re.search(pattern, file_name, re.IGNORECASE)
        if match:
            facility_name = match.group(1).replace('.xlsx', '').replace('.xls', '').strip()
            if len(facility_name) > 2:  # 确保名称有意义
                return facility_name

    # 方法2: 从工作表名称提取
    if sheet_name and sheet_name != 'Sheet1' and len(sheet_name) > 2:
        return sheet_name

    # 方法3: 从列名推断设施类型
    facility_type = infer_facility_type_from_columns(columns)

    # 方法4: 从文件路径提取目录信息
    path_parts = Path(file_name).parts
    for part in reversed(path_parts[:-1]):  # 排除文件名本身
        if len(part) > 2 and not part.isdigit():
            return f"{part}{facility_type}"

    # 默认返回
    return f"水利设施{facility_type}"

def infer_facility_type_from_columns(columns):
    """从列名推断设施类型"""
    column_str = ' '.join(str(col) for col in columns).lower()

    # 设施类型关键词映射
    facility_keywords = {
        '引水闸': ['引水', '进水', '取水'],
        '节制闸': ['节制', '控制', '调节'],
        '渡槽': ['渡槽', '输水', '过水'],
        '泵站': ['泵站', '提升', '抽水'],
        '水库': ['库容', '蓄水', '水库'],
        '闸门': ['闸门', '启闭', '开度'],
        '监测站': ['监测', '观测', '测量']
    }

    for facility_type, keywords in facility_keywords.items():
        if any(keyword in column_str for keyword in keywords):
            return facility_type

    # 根据数据特征推断
    if '流量' in column_str and '水位' in column_str:
        if '开度' in column_str:
            return '闸门'
        else:
            return '监测站'

    return '设施'

def extract_metrics_smart(row, columns):
    """智能提取关键指标 - 通用方法"""

    # 定义指标模式（更通用的匹配）
    metric_patterns = {
        # 流量相关
        'flow': {
            'patterns': [r'.*流量.*', r'.*discharge.*', r'.*flow.*'],
            'unit': 'm³/s',
            'priority': 1
        },
        # 水位相关
        'water_level': {
            'patterns': [r'.*水位.*', r'.*level.*', r'.*elevation.*'],
            'unit': 'm',
            'priority': 2
        },
        # 开度相关
        'opening': {
            'patterns': [r'.*开度.*', r'.*opening.*', r'.*gate.*'],
            'unit': 'mm',
            'priority': 3
        },
        # 压力相关
        'pressure': {
            'patterns': [r'.*压力.*', r'.*pressure.*'],
            'unit': 'MPa',
            'priority': 4
        },
        # 温度相关
        'temperature': {
            'patterns': [r'.*温度.*', r'.*temp.*'],
            'unit': '°C',
            'priority': 5
        }
    }

    extracted_metrics = []

    # 按优先级排序
    sorted_metrics = sorted(metric_patterns.items(), key=lambda x: x[1]['priority'])

    for _, metric_info in sorted_metrics:
        for col in columns:
            col_str = str(col).lower()

            # 检查是否匹配任何模式
            for pattern in metric_info['patterns']:
                if re.search(pattern, col_str, re.IGNORECASE):
                    if col in row.index:
                        value = row[col]
                        if pd.notna(value) and str(value).strip():
                            try:
                                # 格式化数值
                                formatted_metric = format_metric_value(
                                    col, value, metric_info['unit']
                                )
                                if formatted_metric:
                                    extracted_metrics.append(formatted_metric)
                                    break  # 找到匹配后跳出模式循环
                            except Exception:
                                continue
                    break  # 找到匹配的列后跳出模式循环

    return extracted_metrics[:4]  # 最多返回4个关键指标

def format_metric_value(col_name, value, default_unit):
    """格式化指标值"""
    try:
        # 处理特殊格式（如开度的分数形式）
        if '/' in str(value):
            return f"{col_name}{str(value)}{default_unit}"

        # 尝试转换为数值
        num_val = float(str(value))

        # 根据数值大小决定精度
        if abs(num_val) >= 1000:
            formatted_val = f"{num_val:.0f}"
        elif abs(num_val) >= 1:
            formatted_val = f"{num_val:.2f}"
        else:
            formatted_val = f"{num_val:.3f}"

        return f"{col_name}{formatted_val}{default_unit}"

    except:
        return f"{col_name}{str(value)}"

def extract_time_info_smart(row, columns):
    """智能提取时间信息"""
    time_patterns = [
        r'.*时间.*', r'.*date.*', r'.*time.*',
        r'.*日期.*', r'.*datetime.*'
    ]

    for col in columns:
        col_str = str(col).lower()
        for pattern in time_patterns:
            if re.search(pattern, col_str, re.IGNORECASE):
                if col in row.index:
                    time_val = row[col]
                    if pd.notna(time_val) and str(time_val).strip():
                        return str(time_val)

    return ""

class TimeRangeRetriever(BaseRetriever):
    """智能时间范围检索器 - 从根本上解决时间范围查询问题"""

    def __init__(self, base_retriever, index, similarity_top_k: int = 20):
        self.base_retriever = base_retriever
        self.index = index
        self.similarity_top_k = similarity_top_k
        super().__init__()

    def _retrieve(self, query_bundle: QueryBundle) -> List[NodeWithScore]:
        """核心检索逻辑"""
        query = query_bundle.query_str
        print(f"🔍 TimeRangeRetriever收到查询: {query}")

        # 1. 检测是否为时间范围查询
        time_range_info = self._extract_time_range(query)

        if time_range_info:
            print(f"✅ 识别为时间范围查询: {time_range_info}")
            # 时间范围查询：使用专门的时间范围检索策略
            return self._retrieve_time_range(query_bundle, time_range_info)
        else:
            print("⚪ 识别为普通查询，使用基础检索器")
            # 普通查询：使用原有检索器
            return self.base_retriever.retrieve(query_bundle)

    def _extract_time_range(self, query: str) -> dict:
        """提取查询中的时间范围信息"""
        time_patterns = [
            # 完整时间格式：2020-01-01 12:00:00至2020-01-02 12:00:00
            r'(\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2})\s*(?:至|到|~|-|—)\s*(\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2})',
            # 标准格式：2020-01-01至2020-01-02
            r'(\d{4}-\d{2}-\d{2})\s*(?:至|到|~|-|—)\s*(\d{4}-\d{2}-\d{2})',
            # 中文格式：2020年1月1日至2日
            r'(\d{4})年(\d{1,2})月(\d{1,2})日\s*(?:至|到|~|-|—)\s*(\d{1,2})日',
            # 混合格式：1月1日12:00至1月2日12:00
            r'(\d{1,2})月(\d{1,2})日\s*(\d{1,2}):(\d{2})\s*(?:至|到|~|-|—)\s*(\d{1,2})月(\d{1,2})日\s*(\d{1,2}):(\d{2})',
        ]

        for pattern in time_patterns:
            match = re.search(pattern, query)
            if match:
                groups = match.groups()
                if len(groups) >= 2:
                    return {
                        'has_time_range': True,
                        'start_time': groups[0],
                        'end_time': groups[1] if len(groups) >= 2 else groups[0],
                        'pattern_type': 'standard' if '-' in groups[0] else 'chinese',
                        'original_query': query
                    }

        # 检测单个时间点查询
        single_time_patterns = [
            r'(\d{4}-\d{2}-\d{2})\s+(\d{2}):(\d{2}):(\d{2})',
            r'(\d{4})年(\d{1,2})月(\d{1,2})日\s*(\d{1,2})(?:点|时)',
        ]

        for pattern in single_time_patterns:
            if re.search(pattern, query):
                return {
                    'has_time_range': False,
                    'single_time': True,
                    'original_query': query
                }

        return None

    def _retrieve_time_range(self, query_bundle: QueryBundle, time_info: dict) -> List[NodeWithScore]:
        """专门处理时间范围查询的检索逻辑"""
        query = query_bundle.query_str
        print(f"🎯 执行时间范围检索，时间信息: {time_info}")

        # 1. 首先尝试检索时序摘要
        summary_queries = [
            f"{query} 运行统计摘要",
            f"{query} 期间运行情况",
            f"{query} 调度运行报告",
            f"时序摘要 {time_info.get('start_time', '')} {time_info.get('end_time', '')}",
        ]

        all_results = []
        print(f"📊 尝试检索时序摘要，查询数量: {len(summary_queries)}")

        # 检索时序摘要
        for summary_query in summary_queries:
            summary_bundle = QueryBundle(query_str=summary_query)
            summary_results = self.base_retriever.retrieve(summary_bundle)

            # 筛选时序摘要节点
            for result in summary_results:
                if (hasattr(result.node, 'metadata') and
                    result.node.metadata.get('content_type') == 'time_series_summary'):
                    all_results.append(result)

        # 2. 如果没有找到摘要，则检索相关的时间点数据
        if not all_results:
            # 扩展查询以包含更多时间相关的关键词
            expanded_queries = [
                query,
                query.replace('至', '').replace('到', ''),  # 移除时间连接词
                f"{query} 数据",
                f"{query} 运行",
                f"{query} 调度",
                # 专门搜索1月1日和1月2日的数据
                "十二里河渡槽 2020-01-01 12:00:00",
                "十二里河渡槽 2020-01-02 12:00:00",
                "十二里河渡槽进口节制闸 2020-01-01",
                "十二里河渡槽进口节制闸 2020-01-02",
            ]

            for expanded_query in expanded_queries:
                expanded_bundle = QueryBundle(query_str=expanded_query)
                results = self.base_retriever.retrieve(expanded_bundle)

                # 筛选Excel数据节点
                for result in results:
                    if (hasattr(result.node, 'metadata') and
                        result.node.metadata.get('content_type') == 'excel_row' and
                        self._is_time_relevant(result.node.text, time_info)):
                        all_results.append(result)

        # 3. 去重并按相关度排序
        seen_texts = set()
        unique_results = []
        for result in all_results:
            if result.node.text not in seen_texts:
                seen_texts.add(result.node.text)
                unique_results.append(result)

        # 按相关度分数排序
        unique_results.sort(key=lambda x: getattr(x, 'score', 0), reverse=True)

        return unique_results[:self.similarity_top_k]

    def _is_time_relevant(self, text: str, time_info: dict) -> bool:
        """判断文本是否与查询的时间范围相关"""
        if not time_info:
            return True

        # 提取文本中的时间信息
        time_patterns = [
            r'(\d{4}-\d{2}-\d{2})\s+(\d{2}):(\d{2}):(\d{2})',
            r'(\d{4})年(\d{1,2})月(\d{1,2})日',
        ]

        start_time = time_info.get('start_time', '')
        end_time = time_info.get('end_time', '')

        # 如果是2020-01-01至2020-01-02的查询，接受这两天的任何数据
        if '2020-01-01' in start_time and '2020-01-02' in end_time:
            if '2020-01-01' in text or '2020-01-02' in text:
                return True

        # 通用的时间匹配逻辑
        for pattern in time_patterns:
            matches = re.findall(pattern, text)
            if matches:
                for match in matches:
                    time_str = match[0] if isinstance(match, tuple) else str(match)
                    if start_time in time_str or end_time in time_str:
                        return True

        return False

class ContextEnhancer:
    """上下文增强器 - 从根本上解决数据不完整问题"""

    @staticmethod
    def enhance_time_range_context(nodes: List[NodeWithScore], query: str) -> str:
        """增强时间范围查询的上下文"""
        if not nodes:
            return ""

        # 提取查询中的时间范围
        time_range_match = re.search(r'(\d{4}-\d{2}-\d{2}).*?(\d{4}-\d{2}-\d{2})', query)
        if not time_range_match:
            return ContextEnhancer._format_standard_context(nodes)

        start_date = time_range_match.group(1)
        end_date = time_range_match.group(2)

        # 分析现有数据
        available_data = []
        for node in nodes:
            text = node.text
            # 提取时间信息
            time_matches = re.findall(r'(\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2})', text)
            for time_str in time_matches:
                if start_date in time_str or end_date in time_str:
                    available_data.append({
                        'time': time_str,
                        'content': text,
                        'score': getattr(node, 'score', 0)
                    })

        if not available_data:
            return ContextEnhancer._format_standard_context(nodes)

        # 构建增强的上下文
        enhanced_context = f"**时间范围分析：{start_date} 至 {end_date}**\n\n"
        enhanced_context += "**可用数据点：**\n"

        # 按时间排序
        available_data.sort(key=lambda x: x['time'])

        for i, data in enumerate(available_data, 1):
            enhanced_context += f"{i}. {data['content']}\n\n"

        # 添加分析指导
        enhanced_context += "**分析指导：**\n"
        enhanced_context += f"- 查询时间范围：{start_date} 至 {end_date}\n"
        enhanced_context += f"- 可用数据点：{len(available_data)} 个\n"
        enhanced_context += "- 请基于现有数据点进行完整的时间范围分析\n"
        enhanced_context += "- 对于数据间隔，可基于相邻时间点进行合理推断\n"
        enhanced_context += "- 重点提供运行趋势和关键指标变化分析\n\n"

        return enhanced_context

    @staticmethod
    def _format_standard_context(nodes: List[NodeWithScore]) -> str:
        """格式化标准上下文"""
        context_parts = []
        for i, node in enumerate(nodes, 1):
            context_parts.append(f"{i}. {node.text}")
        return "\n\n".join(context_parts)

def create_time_series_summary(df, facility_name, time_col, metrics_cols):
    """创建时序数据摘要节点"""
    try:
        if df.empty or len(df) < 2:
            return []

        # 按时间排序
        df_sorted = df.sort_values(by=time_col)

        # 创建不同时间粒度的摘要
        summaries = []

        # 1. 整体摘要
        start_time = df_sorted[time_col].iloc[0]
        end_time = df_sorted[time_col].iloc[-1]

        summary_parts = [f"{facility_name}在{start_time}至{end_time}期间的运行统计摘要"]

        # 分析关键指标
        for col in metrics_cols:
            if col in df_sorted.columns:
                values = pd.to_numeric(df_sorted[col], errors='coerce').dropna()
                if not values.empty:
                    col_name = str(col)
                    if '流量' in col_name:
                        summary_parts.append(f"流量范围{values.min():.2f}-{values.max():.2f}m³/s，平均{values.mean():.2f}m³/s")
                    elif '水位' in col_name:
                        summary_parts.append(f"水位范围{values.min():.2f}-{values.max():.2f}m，平均{values.mean():.2f}m")
                    elif '开度' in col_name:
                        summary_parts.append(f"开度范围{values.min():.0f}-{values.max():.0f}mm，平均{values.mean():.0f}mm")

        summary_parts.append(f"数据点数量{len(df_sorted)}个，时间间隔约{_calculate_time_interval(df_sorted, time_col)}")

        overall_summary = "；".join(summary_parts)
        summaries.append({
            "content": overall_summary,
            "metadata": {
                "content_type": "time_series_summary",
                "facility_name": facility_name,
                "time_range": f"{start_time}至{end_time}",
                "data_points": len(df_sorted),
                "summary_type": "overall"
            }
        })

        # 2. 按日期分组的摘要（如果数据跨多天）
        df_sorted['date'] = pd.to_datetime(df_sorted[time_col]).dt.date
        daily_groups = df_sorted.groupby('date')

        if len(daily_groups) > 1:
            for date, group in daily_groups:
                if len(group) > 1:
                    daily_summary = _create_daily_summary(group, facility_name, time_col, metrics_cols, date)
                    if daily_summary:
                        summaries.append(daily_summary)

        return summaries

    except Exception as e:
        print(f"时序摘要生成失败: {str(e)}")
        return []

def _calculate_time_interval(df, time_col):
    """计算时间间隔"""
    try:
        times = pd.to_datetime(df[time_col])
        if len(times) > 1:
            intervals = times.diff().dropna()
            avg_interval = intervals.mean()

            if avg_interval.total_seconds() < 3600:  # 小于1小时
                return f"{int(avg_interval.total_seconds()/60)}分钟"
            elif avg_interval.total_seconds() < 86400:  # 小于1天
                return f"{int(avg_interval.total_seconds()/3600)}小时"
            else:
                return f"{int(avg_interval.total_seconds()/86400)}天"
        return "未知"
    except:
        return "未知"

def _create_daily_summary(group, facility_name, time_col, metrics_cols, date):
    """创建日摘要"""
    try:
        summary_parts = [f"{facility_name}在{date}的运行摘要"]

        for col in metrics_cols:
            if col in group.columns:
                values = pd.to_numeric(group[col], errors='coerce').dropna()
                if not values.empty:
                    col_name = str(col)
                    if '流量' in col_name:
                        summary_parts.append(f"日均流量{values.mean():.2f}m³/s")
                    elif '水位' in col_name and '闸前' in col_name:
                        summary_parts.append(f"日均闸前水位{values.mean():.2f}m")

        summary_parts.append(f"共{len(group)}个数据点")

        return {
            "content": "；".join(summary_parts),
            "metadata": {
                "content_type": "daily_summary",
                "facility_name": facility_name,
                "date": str(date),
                "data_points": len(group),
                "summary_type": "daily"
            }
        }
    except:
        return None

def create_enhanced_excel_description(row, columns, file_name, sheet_name, row_number):
    """为Excel数据创建增强的语义描述 - 智能通用版本"""
    try:
        # 智能提取设施名称（通用方法）
        facility_name = extract_facility_name_smart(file_name, sheet_name, columns)

        # 智能提取时间信息
        time_info = extract_time_info_smart(row, columns)

        # 智能提取关键指标
        key_metrics = extract_metrics_smart(row, columns)

        # 构建增强描述
        desc_parts = [facility_name]

        if time_info:
            desc_parts.append(f"在{time_info}")

        desc_parts.append("的运行数据")

        if key_metrics:
            desc_parts.append(f": {', '.join(key_metrics)}")

        enhanced_desc = "".join(desc_parts)

        # 添加数据来源信息
        source_info = f"(数据来源: {Path(file_name).name}, 第{row_number}行)"

        return f"{enhanced_desc} {source_info}"

    except Exception as e:
        # 如果增强描述生成失败，返回基础描述
        print(f"Excel描述生成失败: {str(e)}")
        return f"数据记录 - 文件: {file_name}, 工作表: {sheet_name}, 行号: {row_number}"

def load_excels(data_dir: str) -> List[Dict]:
    """
    优化的Excel加载函数 v4。
    1. 使用并行处理加速多文件处理
    2. 使用LRU缓存减少重复计算
    3. 批量处理数据以提高效率
    4. 智能判断数据类型减少转换错误
    """
    excel_files = list(Path(data_dir).rglob("*.xlsx"))
    all_data = []
    
    # 定义日期格式的缓存函数
    @lru_cache(maxsize=128)
    def format_datetime(value_str):
        try:
            # 先尝试标准解析
            dt = pd.to_datetime(value_str, errors='coerce')
            if pd.notna(dt):
                return dt.strftime('%Y-%m-%d %H:%M:%S')

            # 如果标准解析失败，尝试处理特殊格式
            # 处理 "2020-01-01 12" 这种格式（缺少 :00:00）
            if isinstance(value_str, str) and len(value_str.strip()) > 0:
                # 匹配 "YYYY-MM-DD HH" 格式
                import re
                pattern = r'^(\d{4}-\d{2}-\d{2})\s+(\d{1,2})$'
                match = re.match(pattern, value_str.strip())
                if match:
                    date_part = match.group(1)
                    hour_part = match.group(2).zfill(2)  # 补零到2位
                    standardized = f"{date_part} {hour_part}:00:00"
                    # 再次尝试解析标准化后的时间
                    dt = pd.to_datetime(standardized, errors='coerce')
                    if pd.notna(dt):
                        return dt.strftime('%Y-%m-%d %H:%M:%S')
        except Exception:
            pass
        return value_str
    
    # 智能处理单个值
    def process_value(col, value_str):
        # 如果值为空，直接返回"未知"
        if pd.isna(value_str) or value_str == "":
            return "未知"
            
        # 处理日期时间类型
        if isinstance(value_str, str) and ('时间' in str(col) or '日期' in str(col)):
            return format_datetime(value_str)
            
        # 处理数值类型
        if isinstance(value_str, str):
            try:
                value_float = float(value_str)
                # 只对非整数进行四舍五入
                if value_float.is_integer():
                    return str(int(value_float))
                else:
                    return str(round(value_float, 3))
            except (ValueError, TypeError):
                pass
                
        return str(value_str)

    # 处理单个Excel文件
    def process_excel_file(excel_file):
        file_data = []
        try:
            # 使用engine='openpyxl'提高与新版Excel的兼容性
            sheets = pd.read_excel(excel_file, sheet_name=None, dtype=str, engine='openpyxl')
            
            for sheet_name, df in sheets.items():
                if df.empty:
                    continue
                
                # 清理列名
                df.columns = df.columns.map(str)
                columns = df.columns.tolist()
                
                # 预处理整个数据框，而不是逐行处理
                # 优化1: 批量处理时间列
                time_cols = [col for col in columns if '时间' in str(col) or '日期' in str(col)]
                for col in time_cols:
                    if col in df.columns:
                        df[col] = df[col].apply(lambda x: format_datetime(x) if pd.notna(x) and isinstance(x, str) else x)
                
                # 1. 生成时序摘要（如果数据足够多）
                if len(df) > 2:  # 降低阈值，至少3个数据点就生成摘要
                    facility_name = extract_facility_name_smart(excel_file.name, sheet_name, columns)
                    time_col = None

                    # 找到时间列
                    for col in time_cols:
                        if col in df.columns:
                            time_col = col
                            break

                    if time_col:
                        # 找到数值列作为指标
                        metrics_cols = []
                        for col in columns:
                            if any(keyword in str(col) for keyword in ['流量', '水位', '开度', '压力', '温度']):
                                metrics_cols.append(col)

                        # 生成时序摘要
                        summaries = create_time_series_summary(df, facility_name, time_col, metrics_cols)
                        file_data.extend(summaries)

                # 2. 处理每行数据
                for idx, row in df.iterrows():
                    # 构建键值对列表
                    parts = [f"'{col}': '{process_value(col, row[col])}'" for col in columns]

                    # 生成增强的语义描述
                    enhanced_description = create_enhanced_excel_description(row, columns, excel_file.name, sheet_name, idx + 2)

                    # 采用结构化文本格式（保持原格式作为备用）
                    row_description = f"{enhanced_description}\n原始数据: {{{', '.join(parts)}}}"

                    file_data.append({
                        "content": row_description,
                        "metadata": {
                            "source_file": str(excel_file.relative_to(data_dir)),
                            "content_type": "excel_row",
                            "sheet_name": sheet_name,
                            "row_number": idx + 2
                        }
                    })
                    
        except Exception as e:
            print(f"加载或处理Excel文件 {excel_file} 时出错: {str(e)}")
        return file_data
    
    # 并行处理所有Excel文件
    with concurrent.futures.ThreadPoolExecutor(max_workers=min(8, len(excel_files))) as executor:
        results = list(executor.map(process_excel_file, excel_files))
    
    # 合并所有结果
    for result in results:
        all_data.extend(result)
    
    print(f"成功从Excel文件中加载并处理了 {len(all_data)} 行数据。")
    return all_data

def create_nodes_from_text(raw_data: List[Dict], node_parser) -> List[TextNode]:
    all_nodes = []
    for entry in raw_data:
        content = entry["content"]
        source_file = entry["metadata"].get("source_file", "unknown_source")

        doc = Document(text=content, metadata={"source_file": source_file, "content_type": "text_document"})
        nodes = node_parser.get_nodes_from_documents([doc])

        for i, node in enumerate(nodes):
            node.id_ = f"{source_file}::chunk_{i}"
            node.metadata.update({
                "source_file": source_file,
                "content_type": "text_document_chunk"
            })
            all_nodes.append(node)

    return all_nodes


# ===========专门处理PDF文本的节点生成函数 ==========

# 添加专门处理PDF文件的节点生成函数
def create_nodes_from_pdf(raw_data: List[Dict], node_parser) -> List[TextNode]:
    all_nodes = []
    warning_count = 0

    for entry in raw_data:
        content = entry["content"]
        if not content or len(content.strip()) < 10:
            source = entry["metadata"].get("source_file", "未知文件")
            print(f"警告：PDF文件 {source} 内容为空，已跳过")
            warning_count += 1
            continue

        source_file = entry["metadata"]["source_file"]

        doc = Document(text=content, metadata={
            "source_file": source_file,
            "content_type": "pdf_document"
        })

        try:
            nodes = node_parser.get_nodes_from_documents([doc])
            for i, node in enumerate(nodes):
                node.id_ = f"{source_file}::chunk_{i}"
                node.metadata.update({
                    "source_file": source_file,
                    "content_type": "pdf_document_chunk"
                })
                all_nodes.append(node)
        except Exception as e:
            print(f"处理PDF文件 {source_file} 时出错: {str(e)}")

    if warning_count > 0:
        print(f"注意: {warning_count} 个PDF文件因内容为空被跳过")

    print(f"成功从PDF内容生成 {len(all_nodes)} 个文本节点。")
    return all_nodes
def create_nodes_from_excel(raw_data: List[Dict], node_parser) -> List[TextNode]:
    """
    为Excel的每一行数据创建一个TextNode。
    优化处理以更高效地创建节点并保持唯一ID。
    支持并行处理以加快处理速度。
    """
    if not raw_data:
        print("警告: 没有Excel数据可以处理")
        return []
    
    all_nodes = []
    excel_entries = [entry for entry in raw_data if entry.get("metadata", {}).get("content_type") == "excel_row"]
    
    # 按文件和工作表分组，以便更好地组织节点
    file_sheet_groups = {}
    for entry in excel_entries:
        metadata = entry["metadata"]
        file_key = metadata.get("source_file", "unknown_file")
        sheet_key = metadata.get("sheet_name", "unknown_sheet")
        key = (file_key, sheet_key)
        
        if key not in file_sheet_groups:
            file_sheet_groups[key] = []
        file_sheet_groups[key].append(entry)
    
    # 处理每个文件-工作表组
    for (file_key, sheet_key), entries in file_sheet_groups.items():
        # 为每个组创建统一的前缀
        prefix = f"{file_key}::{sheet_key}"
        
        # 创建节点
        for entry in entries:
            metadata = entry["metadata"]
            row_number = metadata.get("row_number", "unknown_row")
            node_id = f"{prefix}::row_{row_number}"
            
            node = TextNode(
                text=entry["content"],
                id_=node_id,
                metadata=metadata
            )
            all_nodes.append(node)
    
    print(f"从Excel数据中创建了 {len(all_nodes)} 个文本节点")
    return all_nodes


# ================== 界面组件 ==================
# 修改: 使用自定义的HTML/CSS渲染聊天气泡
def display_chat_message(message):
    """根据角色渲染用户或助手的聊天气泡"""
    role = message["role"]
    content = message.get("cleaned", message["content"])

    if role == "user":
        st.markdown(f'<div class="user-bubble">{content}</div>', unsafe_allow_html=True)
    elif role == "assistant":
        st.markdown(f'<div class="assistant-bubble">{content}</div>', unsafe_allow_html=True)

        # 将思考过程和参考文献的展开框放在气泡下方
        if message.get("think"):
            with st.expander("📝 查看模型思考过程"):
                for think_content in message["think"]:
                    st.info(think_content.strip())

        if "reference_nodes" in message and message["reference_nodes"]:
            show_reference_details(message["reference_nodes"])

    # 添加一个清除浮动的div，防止布局错乱
    st.markdown('<div style="clear: both;"></div>', unsafe_allow_html=True)


def init_chat_interface():
    if "messages" not in st.session_state:
        st.session_state.messages = []

    for msg in st.session_state.messages:
        role = msg["role"]
        content = msg.get("cleaned", msg["content"])  # 优先使用清理后的内容

        with st.chat_message(role):
            st.markdown(content)

            # 如果是助手消息且包含思维链
            if role == "assistant" and msg.get("think"):
                with st.expander("📝 模型思考过程（历史对话）"):
                    for think_content in msg["think"]:
                        st.markdown(f'<span style="color: #808080">{think_content.strip()}</span>',
                                    unsafe_allow_html=True)

            # 如果是助手消息且有参考依据（需要保持原有参考依据逻辑）
            if role == "assistant" and "reference_nodes" in msg:
                show_reference_details(msg["reference_nodes"])


# 修改: 优化参考文献的显示样式
def show_reference_details(nodes):
    with st.expander("📚 查看参考文献"):
        for idx, node in enumerate(nodes, 1):
            meta = node.node.metadata
            source_file = meta.get("source_file", "未知文件")

            st.markdown(f"""
            <div class="reference-expander">
                <p><b>[{idx}] 来源:</b> {source_file}</p>
                <p><b>相关度:</b> {node.score:.4f}</p>
                <p><b>内容片段:</b></p>
                <blockquote>{node.node.text}</blockquote>
            </div>
            """, unsafe_allow_html=True)


# 新增：记录系统无法回答的问题
def log_unanswerable_question(question: str, context_nodes=None):
    """
    记录系统无法回答的问题，以便后续改进知识库
    
    Args:
        question: 用户提问的问题
        context_nodes: 检索到的上下文节点（如果有）
    """
    try:
        log_dir = Path("./logs")
        log_dir.mkdir(exist_ok=True)
        
        log_file = log_dir / "unanswerable_questions.jsonl"
        
        # 准备日志条目
        log_entry = {
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "question": question,
            "retrieved_contexts": []
        }
        
        # 如果有上下文节点，记录它们的信息
        if context_nodes:
            for node in context_nodes:
                log_entry["retrieved_contexts"].append({
                    "text": node.node.text[:200] + "..." if len(node.node.text) > 200 else node.node.text,
                    "source": node.node.metadata.get("source_file", "未知来源"),
                    "score": node.score if hasattr(node, "score") else None
                })
        
        # 追加到日志文件
        with open(log_file, "a", encoding="utf-8") as f:
            f.write(json.dumps(log_entry, ensure_ascii=False) + "\n")
            
        print(f"已记录无法回答的问题: {question}")
    except Exception as e:
        print(f"记录无法回答的问题时出错: {str(e)}")


# ================== 主程序 ==================
def main():
    load_css("frontend/style.css")

    # 初始化按钮计数器
    if "button_counter" not in st.session_state:
        st.session_state.button_counter = 0

    # 确保messages已初始化
    if "messages" not in st.session_state:
        st.session_state.messages = []

    # 🔧 关键修复：在任何操作之前先初始化模型管理器并设置全局Settings
    if "model_manager" not in st.session_state:
        st.session_state.model_manager = OptimizedModelManager()
        # 立即获取所有模型以设置全局Settings（这是必需的）
        _ = st.session_state.model_manager.get_embed_model()
        _ = st.session_state.model_manager.get_llm()  # 也需要设置LLM

    if "query_cache" not in st.session_state:
        st.session_state.query_cache = QueryEmbeddingCache()

    # --- 侧边栏 ---
    with st.sidebar:
        st.image("assets/Picture/lixiahe.png", width=80)
        st.title("控制与信息")
        st.info("欢迎使用南水北调水利问答助手。本系统旨在提供专业、准确的信息。")

        # 清除聊天记录按钮 - 添加唯一key
        if st.button("清除聊天记录", use_container_width=True, type="primary", key="clear_chat_button"):
            st.session_state.messages = []
            # 同时清除后端的对话内存
            if "chat_engine" in st.session_state:
                st.session_state.chat_engine.reset()
            st.rerun()

        # 导出整个对话为文本文件 - 添加唯一key
        if st.session_state.messages and st.button("导出对话为文本", use_container_width=True,
                                                   key="export_all_text_button"):
            try:
                content = "南水北调水利问答记录\n\n"

                for msg in st.session_state.messages:
                    role = msg["role"]
                    text = msg.get("cleaned", msg["content"])

                    if role == "user":
                        content += f"问题:\n{text}\n\n"
                    elif role == "assistant":
                        content += f"回答:\n{text}\n\n"

                content += f"生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}"
                text_bytes = content.encode('utf-8')

                timestamp = time.strftime("%Y%m%d-%H%M%S")
                b64 = base64.b64encode(text_bytes).decode()
                href = f'<a href="data:text/plain;charset=utf-8;base64,{b64}" download="南水北调问答-{timestamp}.txt">点击下载文本文件</a>'
                st.markdown(href, unsafe_allow_html=True)
            except Exception as e:
                st.error(f"生成文本时出错: {str(e)}")

    # --- 主界面 ---
    st.title("南水北调水利问答助手 💧")
    st.markdown("请输入您的问题，我们将基于最新的研究成果和工程实践为您解答。")

    # 使用优化的模型管理器和查询缓存（已在main函数开头初始化）
    model_manager = st.session_state.model_manager
    query_cache = st.session_state.query_cache

    # 加载知识库索引
    index = load_index()

    # 如果加载失败，显示错误并停止应用
    if index is None:
        st.error(
            "未能加载知识库。请先在终端运行 'python build_knowledge_base.py' 来构建知识库。"
        )
        st.stop()  # 停止执行


    # --- 对话引擎核心修改：使用 QueryFusionRetriever (官方推荐方案) ---
    if "chat_engine" not in st.session_state:
        with st.spinner("正在初始化高级对话引擎（首次启动较慢）..."):
            # 1. 初始化两种检索器：向量检索器和关键字检索器
            vector_retriever = index.as_retriever(similarity_top_k=Config.TOP_K)
            
            # 修复：直接从docstore中获取节点，确保BM25Retriever能拿到数据
            nodes = list(index.docstore.docs.values())
            if not nodes:
                st.error("知识库的文档库(docstore)为空，无法初始化关键字检索器。请重新构建知识库。")
                st.stop()

            bm25_retriever = BM25Retriever.from_defaults(
                nodes=nodes, 
                similarity_top_k=Config.TOP_K
            )
            
            # 2. 安全的融合检索器实现（带降级机制）
            # 获取LLM模型用于查询融合
            llm = model_manager.get_llm()

            # 尝试启用融合检索，失败时降级到向量检索
            try:
                print("🔄 尝试初始化融合检索器...")
                base_fusion_retriever = QueryFusionRetriever(
                    retrievers=[vector_retriever, bm25_retriever],
                    similarity_top_k=Config.TOP_K,
                    num_queries=2,  # 减少到2个查询，平衡性能和效果
                    use_async=False,  # 暂时禁用异步，避免冲突
                    verbose=False,    # 关闭详细输出，减少日志噪音
                    llm=llm         # 使用优化的LLM进行查询扩展
                )

                # 🎯 关键改进：使用智能时间范围检索器包装融合检索器
                retriever = TimeRangeRetriever(
                    base_retriever=base_fusion_retriever,
                    index=index,
                    similarity_top_k=Config.TOP_K
                )
                print("✅ 融合检索器初始化成功")
                print("🎯 智能时间范围检索器已启用")
            except Exception as e:
                print(f"⚠️ 融合检索器初始化失败，降级到向量检索: {str(e)}")
                # 即使降级，也使用智能时间范围检索器
                retriever = TimeRangeRetriever(
                    base_retriever=vector_retriever,
                    index=index,
                    similarity_top_k=Config.TOP_K
                )

            # 2.5 添加智能查询扩展功能
            def expand_time_range_query(query):
                """扩展时间范围查询，提高检索召回率"""
                import re

                # 检测时间范围查询模式
                time_range_patterns = [
                    r'(\d{4}-\d{2}-\d{2})\s*(?:至|到|~|-)\s*(\d{4}-\d{2}-\d{2})',
                    r'(\d{4}年\d{1,2}月\d{1,2}日)\s*(?:至|到|~|-)\s*(\d{4}年\d{1,2}月\d{1,2}日)',
                ]

                expanded_queries = [query]  # 原始查询

                for pattern in time_range_patterns:
                    matches = re.findall(pattern, query)
                    if matches:
                        for start_time, end_time in matches:
                            # 添加摘要相关的查询词
                            expanded_queries.extend([
                                f"{query} 期间 运行统计",
                                f"{query} 时间范围 摘要",
                                f"{query} 调度运行",
                                f"运行统计摘要 {start_time} {end_time}",
                                f"期间运行情况 {start_time} {end_time}"
                            ])
                            break

                return expanded_queries

            # 3. 定义并保存我们最严格的提示词模板
            QA_PROMPT_TMPL_STR = (
                "你是一个高度严谨的南水北调水利问答机器人，你的任务是只使用下面提供的上下文信息来回答问题。\n"
                "**规则:**\n"
                "1. 你的回答必须完全且仅基于上下文信息，严禁使用任何外部知识或进行推测。\n"
                "2. 对于你的回答中的每一个观点、事实或数据，都必须在句末明确标注其来源，格式为 `[引用来源 n]`，其中n是上下文信息中对应条目的编号。\n"
                "3. 如果多个来源支持同一个观点，可以标注多个来源，如 `[引用来源 1, 3]`。\n"
                "4. 如果上下文中没有足够信息回答问题，必须直接回答：'根据提供的资料，我无法回答这个问题。' 不要尝试推测或使用你的背景知识。\n"
                "5. 对于时间范围查询，请充分利用上下文中的所有相关数据点，即使不是完整的时间序列，也要基于现有数据提供有价值的分析。\n"
                "6. 不要轻易声称数据缺失，除非上下文中确实完全没有相关信息。如果有部分相关数据，请基于这些数据进行分析。\n"
                "7. 如果问题完全超出南水北调工程和水利领域，请回答：'这个问题超出了我的专业范围，我主要负责回答关于南水北调工程和水利相关的问题。'\n\n"
                "**上下文信息:**\n"
                "---------------------\n"
                "{context_str}\n"
                "---------------------\n"
                "**特别注意 - 时间范围查询处理:**\n"
                "对于涉及时间范围的查询（如'2020年1月1日至2日期间'），请按以下原则处理：\n"
                "1. 如果上下文中有该时间范围内的任何数据点，都要充分利用\n"
                "2. 即使只有部分时间点的数据，也要基于现有数据进行分析和推断\n"
                "3. 绝对不要说'数据缺失'或'暂缺'，而要说'基于现有数据分析'\n"
                "4. 对于缺少的时间点，可以基于相邻时间点的数据进行合理推断\n"
                "5. 重点是提供有价值的分析，而不是强调数据的不完整性\n\n"
                "**思考过程:**\n"
                "请在 <think> 标签中，首先逐一分析每个上下文片段与问题的相关性，然后评估上下文信息是否足够回答问题。对于时间范围查询，重点关注是否有时序摘要或相关时间点数据。最后制定一个严格遵循引用规则的回答计划。\n"
                "**回答:**\n"
                "问题: {query_str}\n"
            )

            qa_prompt_tmpl = PromptTemplate(QA_PROMPT_TMPL_STR)

            # 添加相关度阈值过滤器，确保只有相关度足够高的文档才被用于回答问题
            class RelevanceScoreNodePostprocessor:
                """过滤掉相关度低于阈值的节点"""
                def __init__(self, threshold=0.3):
                    self.threshold = threshold
                    
                def postprocess_nodes(self, nodes, query_bundle):
                    # 添加调试信息
                    print(f"🔍 检索调试: 收到 {len(nodes)} 个节点")

                    # 显示所有节点的相关度分数
                    for i, node in enumerate(nodes[:5]):  # 显示前5个
                        score = getattr(node, 'score', 'N/A')
                        content_preview = node.text[:100] if hasattr(node, 'text') else 'N/A'
                        print(f"   节点{i+1}: 分数={score}, 内容={content_preview}...")

                    # 过滤掉相关度低于阈值的节点
                    filtered_nodes = [node for node in nodes if hasattr(node, 'score') and node.score >= self.threshold]

                    print(f"🎯 过滤结果: {len(filtered_nodes)}/{len(nodes)} 个节点通过阈值 {self.threshold}")

                    # 如果过滤后没有节点，返回一个空列表，这将触发"无法回答"逻辑
                    if not filtered_nodes and nodes:
                        print(f"⚠️ 警告：所有检索到的节点相关度都低于阈值 {self.threshold}")
                        # 显示最高分数的节点
                        if nodes:
                            best_node = max(nodes, key=lambda x: getattr(x, 'score', 0))
                            best_score = getattr(best_node, 'score', 0)
                            print(f"   最高分数: {best_score}")
                            print(f"   建议降低阈值到: {best_score * 0.8}")

                    return filtered_nodes

            # 创建相关度过滤器，设置阈值
            relevance_filter = RelevanceScoreNodePostprocessor(
                threshold=Config.RELEVANCE_THRESHOLD
            )

            # 4. 使用强大的融合检索器创建带记忆的聊天引擎
            # 获取重排序模型（LLM已在上面获取）
            reranker = model_manager.get_reranker()

            # 创建自定义的聊天引擎，集成上下文增强器
            class EnhancedContextChatEngine(ContextChatEngine):
                def _get_context_str(self, nodes):
                    """重写上下文获取方法，使用增强器"""
                    # 获取当前查询
                    current_query = getattr(self, '_current_query', '')

                    # 使用上下文增强器
                    enhanced_context = ContextEnhancer.enhance_time_range_context(nodes, current_query)
                    return enhanced_context if enhanced_context else super()._get_context_str(nodes)

                def chat(self, message, chat_history=None):
                    """重写聊天方法，保存当前查询"""
                    self._current_query = message
                    return super().chat(message, chat_history)

            st.session_state.chat_engine = EnhancedContextChatEngine.from_defaults(
                retriever=retriever,
                llm=llm,
                memory=ChatMemoryBuffer.from_defaults(token_limit=4096),
                node_postprocessors=[reranker, relevance_filter],  # 先重排序，再过滤低相关度节点
                context_prompt=qa_prompt_tmpl,
                verbose=True
            )

    # --- 聊天界面 ---
    # 显示历史消息
    for msg in st.session_state.messages:
        display_chat_message(msg)

    # 处理用户输入
    if prompt := st.chat_input("请输入您的问题..."):
        # 将用户消息添加到显示列表并立即显示
        user_message = {"role": "user", "content": prompt}
        st.session_state.messages.append(user_message)
        display_chat_message(user_message)

        # 处理查询并显示助手响应
        # 🚀 使用查询缓存优化：先检查是否为重复查询
        model_manager = st.session_state.model_manager
        query_cache = st.session_state.query_cache

        # 检查缓存中是否有相同查询的完整响应
        cache_key = f"full_response_{prompt}"
        cached_response = query_cache.get_cached_response(cache_key)

        if cached_response:
            # 从缓存获取完整响应 - 应该很快
            with st.spinner("🚀 从缓存获取回答..."):
                response_text = cached_response["response_text"]
                think_contents = cached_response["think_contents"]
                source_nodes = cached_response["source_nodes"]
                print("🚀 从缓存获取完整响应")
                st.success("✨ 使用了缓存加速！")
        else:
            # 执行新的查询并缓存结果 - 会比较慢
            with st.spinner("🔄 首次查询，正在生成回答..."):
                response = st.session_state.chat_engine.chat(prompt)
                response_text = str(response)

                # 提取思考过程和参考文献
                think_contents = re.findall(r'<think>(.*?)</think>', response_text, re.DOTALL)
                source_nodes = response.source_nodes

                # 缓存完整响应
                query_cache.cache_full_response(cache_key, {
                    "response_text": response_text,
                    "think_contents": think_contents,
                    "source_nodes": source_nodes
                })
                print("💾 缓存新的查询响应")
                st.info("💾 已缓存此查询，下次相同问题会更快！")

        # 清理响应文本（无论是从缓存还是新计算的）
        cleaned_response = re.sub(r'<think>.*?</think>', '', response_text, flags=re.DOTALL).strip()

        # 检查是否无法回答问题
        if "我无法回答这个问题" in cleaned_response or "超出了我的专业范围" in cleaned_response:
            # 记录无法回答的问题
            log_unanswerable_question(prompt, source_nodes)

        # 创建助手消息并添加到会话
        assistant_message = {
            "role": "assistant",
            "content": response_text,
            "cleaned": cleaned_response,
            "think": think_contents,
            "reference_nodes": source_nodes
        }
        st.session_state.messages.append(assistant_message)

        # 立即显示助手消息
        display_chat_message(assistant_message)

        # 保存当前问答对，用于文本导出
        st.session_state.last_qa = {
            "question": prompt,
            "answer": cleaned_response
        }

    # 显示下载按钮（对于最近的一次对话）
    if "last_qa" in st.session_state:
        col1, col2 = st.columns([3, 1])
        with col2:
            # 直接准备文本内容，不需要额外的按钮点击
            q = st.session_state.last_qa["question"]
            a = st.session_state.last_qa["answer"]
            content = generate_single_qa_text(q, a).decode('utf-8')
            timestamp = time.strftime("%Y%m%d-%H%M%S")

            # 使用与侧边栏相同的下载按钮实现
            st.download_button(
                label="📄 导出此问答",
                data=content,
                file_name=f"南水北调问答-{timestamp}.txt",
                mime="text/plain",
                key=f"download_single_{len(st.session_state.messages)}_{timestamp}"
            )

if __name__ == "__main__":
    main()